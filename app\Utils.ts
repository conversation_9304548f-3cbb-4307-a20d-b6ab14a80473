import 'react-toastify/dist/ReactToastify.css';
import { v4 as uuidv4 } from 'uuid';
import { toast } from 'react-toastify';
import { isEmail, isMobilePhone } from 'validator';
import { QueryResultRow, sql } from '@vercel/postgres';
import { JC_ListPagingModel, JC_ListPagingResultModel } from './models/ComponentModels/JC_ListPagingModel';


// ----------- //
// - General - //
// ----------- //

export class JC_Utils {

    // Check if URL matches current URL
    static isOnPage(checkUrl?:string) {
        // Check if window is defined (client-side only)
        if (typeof window === 'undefined') {
            return false;
        }
        // Extract just the path portion from the URL
        const pathname = window.location.pathname;
        // Remove leading slash if present in the pathname
        const cleanPathname = pathname.startsWith('/') ? pathname.substring(1) : pathname;
        // Compare with the checkUrl (with or without leading slash)
        return cleanPathname === checkUrl || pathname === `/${checkUrl}`;
    }

    // Check if user is on a mobile device
    static isOnMobile() {
        if (typeof navigator === 'undefined') {
            return false;
        }
        const isMobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        return isMobileDevice;
    }

    // Get responsive path for GIF WebP images (smaller version on small screens)
    static getResponsiveGifPath(imagePath: string): string {
        // Check if the path ends with "Gif.webp"
        if (imagePath.endsWith('Gif.webp')) {
            // If on mobile, use the small version
            if (JC_Utils.isOnMobile()) {
                // Replace "Gif.webp" with "Gif [Small].webp"
                return imagePath.replace('Gif.webp', 'Gif [Small].webp');
            }
        }
        return imagePath;
    }

    // Stringify object then parse it so setState call force trigger rerender
    static parseStringify(theObject:any) {
        return JSON.parse(JSON.stringify(theObject));
    }

    // Random GUID
    static generateGuid() {
        return uuidv4();
    }

    // Check if 2 arrays are equals (does not account for order)
    static arraysEqual(array1:any[], array2:any[]) {
        return array1.length == array2.length && array1.every(x1 => array2.find(x2 => JSON.stringify(x1) == JSON.stringify(x2)));
    }

    // Check if 2 arrays of guid's are equals (does not account for order)
    static guidArraysEqual(array1:string[], array2:string[]) {
        return array1.length == array2.length && array1.every(x1 => array2.find(x2 => JSON.stringify(x1.toLowerCase()) == JSON.stringify(x2.toLowerCase())));
    }

    // Check if string is in a list of strings, ignoring casing
    static stringInListOfStrings(theString:string, theList:string[]) {
        return theList.map(s => s.toLowerCase()).includes(theString.toLowerCase());
    }

    // Convert a list of arrays into a single array
    static flattenArrays(arrays:any[][]) {
        return arrays.flat();
    }

    // Check if string not null and not empty
    static stringNullOrEmpty(inString?:string|null) {
        return inString == undefined || inString == null || inString.trim().length == 0;
    }

    // Round to 2dp and cut off 0's
    static roundAndCutZeroes(num:number, dp:number) {
        if (num == null || num == 0) {
            return 0;
        }
        const newNum = parseFloat(num?.toFixed(dp));
        return Math.round(newNum * 100) / 100;
    }

    // See if search string split by words matches other string
    static searchMatches(searchString:string, checkString:string) {
        let searchWords:string[] = searchString?.toLowerCase().trim().split(' ');
        return searchWords.every(word => checkString.toLowerCase().indexOf(word) > -1);
    }

    // Toast
    static showToastError(text:string) {
        toast.error(text, {
            position: "top-center",
            style: { whiteSpace: 'pre-line' }
        });
    }
    static showToastWarning(text:string) {
        toast.warning(text, {
            position: "top-center",
            style: { whiteSpace: 'pre-line' }
        });
    }
    static showToastSuccess(text:string) {
        toast.success(text, {
            position: "top-center",
            style: { whiteSpace: 'pre-line' }
        });
    }
    static showToastInfo(text:string) {
        toast.info(text, {
            position: "top-center",
            style: { whiteSpace: 'pre-line' }
        });
    }

    // Sleep
    static async sleep(seconds:number) {
        return new Promise(r => setTimeout(r, seconds*1000));
    }

    // Hardcoded list of sound files in public/sounds directory
    private static soundFiles: string[] = [
        "Ahawhaw, My God.wav",
        "Ahhh1.wav",
        "Ahhh2.wav",
        "Ahhh3.wav",
        "Aww, He's Too Busy Lookign At The Dick Head In Front.wav",
        "Aww, You Got All Black Bitches. How I Am I Suppose To Win.wav",
        "Awww, Fuck.wav",
        "Ay, Yeah, Pass It To Him.wav",
        "Ayyaaahh0.wav",
        "Ayyaaahh1.wav",
        "Beautiful...Ahhh.wav",
        "Can't Even Get Past Your Stupid Defence.wav",
        "Eheeeeeh.wav",
        "Ffff, They're Too Black And Fast.wav",
        "For Fuck's Sake.wav",
        "Fuck Off, Chris. Just Fuck Off.wav",
        "Fuck Off, Chris.wav",
        "Fuck's Sake0.wav",
        "Fuck's Sake1.wav",
        "Fuck0.wav",
        "Fuck1.wav",
        "Fuckin faggot.wav",
        "Hahaha.wav",
        "Heeee.wav",
        "I can't do this shit! Ahh!.wav",
        "I Can't Do This.wav",
        "I can't even fucking beat a fucking retard.wav",
        "I Can't Handle This, Chris.wav",
        "I Cant' Get Passed You.wav",
        "I hate this game, it's so shit.wav",
        "I'm losing to a fucking retard.wav",
        "I'm Playing Like Absolute Shit.wav",
        "It gives me shit.wav",
        "mmmm, mmmm, MMM MMM.wav",
        "Moan0.wav",
        "Moan1.wav",
        "Nooo, Nooo.wav",
        "Nooo0.wav",
        "Nooo1.wav",
        "Oh My God.wav",
        "Ohhh now I gotta oooo ahhhh.wav",
        "Ohhhh Nooooo.wav",
        "Ohhhh.wav",
        "Saaay eehh.wav",
        "See How Slow He Was Moving.wav",
        "That Is Crap.wav",
        "This Is Absolute Horse Shit.wav",
        "This is fucked.wav",
        "This Is Fucking Crap.wav",
        "Wawaweewa.wav",
        "What Am I Supposed To Do.wav",
        "Woo.wav",
        "Yeah, Fucking Fantastic.wav",
        "Yeah, Why Dont' You Pass It There. That's A Good Idea.wav"
    ];

    // Play a random sound from the public/sounds directory
    static playRandomSound() {
        try {
            if (this.soundFiles.length === 0) {
                console.error("No sound files found");
                return;
            }

            // Select a random sound file
            const randomIndex = Math.floor(Math.random() * this.soundFiles.length);
            const soundFile = this.soundFiles[randomIndex];

            // Create and play the audio
            const audio = new Audio(`/sounds/${soundFile}`);
            audio.play();
        } catch (error) {
            console.error("Error playing sound:", error);
        }
    }

    // Check if an element is near the center of the screen
    static isElementNearScreenCenter(element: HTMLElement, thresholdPercent: number = 0.2): { isNearCenter: boolean, position: 'above' | 'center' | 'below' } {
        if (!element) return { isNearCenter: false, position: 'below' };

        const rect = element.getBoundingClientRect();
        const windowHeight = window.innerHeight;
        const elementCenter = rect.top + rect.height / 2;
        const windowCenter = windowHeight / 2;

        // Calculate how close the element is to the center (as a percentage of window height)
        const distanceFromCenter = Math.abs(elementCenter - windowCenter) / windowHeight;

        // Determine if element is above, at, or below center
        let position: 'above' | 'center' | 'below';
        if (elementCenter < windowCenter - (windowHeight * thresholdPercent)) {
            position = 'above';
        } else if (elementCenter > windowCenter + (windowHeight * thresholdPercent)) {
            position = 'below';
        } else {
            position = 'center';
        }

        // Check if element is within the threshold percentage of the center
        const isNearCenter = distanceFromCenter < thresholdPercent;

        return { isNearCenter, position };
    }

    // Eg. "productVariation" -> "Product Variations"
    static routeNameToDescription(routeName:string) {
        if (routeName.indexOf('/') >= 0) {
            return routeName;
        } else {
            const result = routeName.replace(/([A-Z])/g, ' $1');
            return result.charAt(0).toUpperCase() + result.slice(1) + 's';
        }
    }

    static getNumOrdinal(num:number) {
        if (num.toString().split('.')[0].slice(-2)[0] == '1') {
            return "th";
        }
        switch (num % 10) {
            case 1:  return "st";
            case 2:  return "nd";
            case 3:  return "rd";
            default: return "th";
        }
    }

}


// --------- //
// - DATES - //
// --------- //

export class JC_Utils_Dates {

    // Get number of minutes between 2 dates
    static minutesBetweenDates(inDate1:Date, inDate2:Date) {
        let date1 = new Date(inDate1);
        let date2 = new Date(inDate2);
        let msBetween = Math.abs(date1.getTime() - date2.getTime());
        const minutesBetween = msBetween / (60 * 1000);
        return minutesBetween;
    }

    // Get formatted date string
    static formattedDateString(inDate:Date) {
        let theDate = new Date(inDate);
        let dateNum = theDate.getDate();
        let ordinal = JC_Utils.getNumOrdinal(dateNum);
        let monthLong = theDate.toLocaleString('default', { month: 'long' });
        let year = theDate.getFullYear();
        return `${dateNum}${ordinal} ${monthLong} ${year}`;
    }

    // Format Date object for Postgres timestamp
    static formatDateForPostgres(inDate:Date) {
        const year    = inDate.getFullYear();
        const month   = (inDate.getMonth() + 1).toString().padStart(2, '0');
        const day     = inDate.getDate().toString().padStart(2, '0');
        const hours   = inDate.getHours().toString().padStart(2, '0');
        const minutes = inDate.getMinutes().toString().padStart(2, '0');
        const seconds = inDate.getSeconds().toString().padStart(2, '0');
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }

}


// ------------ //
// - BUSINESS - //
// ------------ //

export class JC_Utils_Business {

    // Generic Get function for single record retrieval
    static async sqlGet<T extends QueryResultRow>(
        modelConstructor: (new (init?: Partial<T>) => T) & { tableName: string, primaryKey: string },
        pkValue: string,
        filterDeleted: boolean = true
    ): Promise<T> {
        const query = `
            SELECT *
            FROM public."${modelConstructor.tableName}"
            WHERE "${modelConstructor.primaryKey}" = $1
            ${filterDeleted ? `AND "Deleted" = 'False'` : ''}
        `;
        let result = new modelConstructor((await sql.query(query, [pkValue])).rows[0]) as T;
        return result;
    }

    // Generic SQL pagination function with dynamic sorting
    static async sqlGetList<T extends QueryResultRow>(
        modelConstructor: (new (init?: Partial<T>) => T) & { tableName:string, getKeys():string[] },
        whereClause?: string,
        paging?: JC_ListPagingModel,
        filterDeleted: boolean = true
    ): Promise<JC_ListPagingResultModel<T>> {
        // Build WHERE clause combining whereClause and filterDeleted
        let combinedWhereClause = '';
        const conditions: string[] = [];
        if (whereClause) {
            conditions.push(whereClause);
        }
        if (filterDeleted) {
            conditions.push(`"Deleted" = 'False'`);
        }
        if (conditions.length > 0) {
            combinedWhereClause = `WHERE ${conditions.join(' AND ')}`;
        }

        // Build base query
        const baseQuery = `
            SELECT *
            FROM public."${modelConstructor.tableName}"
            ${combinedWhereClause}
        `;

        // Build ORDER BY clause if SortField is provided and valid
        let orderQuery = '';
        if (paging && paging.SortField && modelConstructor.getKeys().includes(paging.SortField)) {
            // Additional security: ensure field name only contains safe characters
            const safeFieldName = paging.SortField.replace(/[^a-zA-Z0-9_]/g, '');
            if (safeFieldName === paging.SortField && safeFieldName.length > 0) {
                const sortDirection = paging.SortAsc ? 'ASC' : 'DESC';
                orderQuery = `ORDER BY "${safeFieldName}" ${sortDirection}`;
            }
        }

        // Check if paging should be ignored (when either PageIndex or PageSize is empty)
        const shouldIgnorePaging = !paging ||
            paging.PageSize === null || paging.PageSize === undefined || paging.PageSize === 0 ||
            paging.PageIndex === null || paging.PageIndex === undefined;

        if (shouldIgnorePaging) {
            // Return all results without paging
            const finalQuery = `${baseQuery} ${orderQuery}`;
            const resultList = (await sql.query(finalQuery)).rows.map(r => new modelConstructor(r)) as T[];
            return {
                ResultList: resultList,
                TotalCount: 0,
                TotalPages: 0
            };
        }

        // Apply paging
        const offset = (paging.PageIndex ?? 0) * (paging.PageSize ?? 20);
        const finalQuery = `${baseQuery} ${orderQuery} LIMIT $1 OFFSET $2`;
        const finalValues = [paging.PageSize, offset];

        // Get the paginated results
        const resultList = (await sql.query(finalQuery, finalValues)).rows.map(r => new modelConstructor(r)) as T[];

        // Get total count using the same WHERE clause
        const countQuery = `
            SELECT COUNT(*) as total
            FROM public."${modelConstructor.tableName}"
            ${combinedWhereClause}
        `;
        const countResult = await sql.query(countQuery);
        const totalCount = parseInt(countResult.rows[0].total);
        const totalPages = Math.ceil(totalCount / (paging.PageSize ?? 20));

        return {
            ResultList: resultList,
            TotalCount: totalCount,
            TotalPages: totalPages
        };
    }

    // Extract paging parameters from URL searchParams
    static getPagingFromParams(
        searchParams: URLSearchParams,
        modelConstructor: { defaultSortField: string }
    ): JC_ListPagingModel | undefined {
        let pageSize = searchParams.get('PageSize');

        if (!pageSize) {
            return undefined;
        }

        return {
            PageSize: searchParams.get('PageSize') ? parseInt(searchParams.get('PageSize')!) : 50,
            PageIndex: searchParams.get('PageIndex') ? parseInt(searchParams.get('PageIndex')!) : 0,
            SortField: searchParams.get('SortField') || modelConstructor.defaultSortField,
            SortAsc: (searchParams.get('SortAsc') || 'true').toLowerCase() !== 'false'
        };
    }

}


// -------------- //
// - VALIDATION - //
// -------------- //

export class JC_Utils_Validation {

    static validEmail(inEmail:string) {
        return isEmail(inEmail);
    }

    static validPhone(inPhone:string) {
        return isMobilePhone(inPhone);
    }

    // Main password validation - checks all requirements
    static validPassword(inPassword:string) {
        return this.validPasswordLength(inPassword)
        && this.validPasswordSymbol(inPassword)
        && this.validPasswordNumber(inPassword);
    }

    // Individual password validation checks
    static validPasswordLength(inPassword:string) {
        return inPassword.length >= 6; // At least 6 characters
    }

    static validPasswordSymbol(inPassword:string) {
        return /[`!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/.test(inPassword); // At least 1 symbol
    }

    static validPasswordNumber(inPassword:string) {
        return /[0-9]+/.test(inPassword); // At least one number
    }

}


// --------------- //
// - PERMISSIONS - //
// --------------- //

export class JC_Utils_Permissions {
    static async checkClipboardPermission(): Promise<boolean> {
        try {
            // Check if navigator is defined (client-side only)
            if (typeof navigator === 'undefined' || typeof navigator.permissions === 'undefined') {
                return false;
            }
            if (navigator.permissions) {
                const permissionStatus = await navigator.permissions.query({ name: 'clipboard-read' as PermissionName });

                if (permissionStatus.state === 'granted') {
                    return true;
                } else if (permissionStatus.state === 'prompt') {
                    try {
                        await navigator.clipboard.readText();
                        JC_Utils.showToastInfo('Permission granted.\nPlease run function again.');
                        return false;
                    } catch (err) {
                        return false;
                    }
                } else {
                    JC_Utils.showToastError('Clipboard access is blocked. Please enable clipboard permission in your browser settings.');
                    return false;
                }
            } else {
                try {
                    await navigator.clipboard.readText();
                    return true;
                } catch (err) {
                    JC_Utils.showToastError('Clipboard access is not available in this browser.');
                    return false;
                }
            }
        } catch (error) {
            console.error('Error checking clipboard permission:', error);
            JC_Utils.showToastError('Failed to check clipboard permission.');
            return false;
        }
    }
}


// ------- //
// - CSS - //
// ------- //

export class JC_Utils_CSS {

    static forceHideHeaderFooter(styles:any) {
        document.getElementById("JC_header")?.classList.add(styles.forceHidden);
        document.getElementById("JC_footer")?.classList.add(styles.forceHidden);
    }

    static forceWhiteBackground(styles:any) {
        document.getElementById("rootMainContainer")?.classList.add(styles.forceWhiteBackground);
    }

    static forceRootOverflowYHidden(styles:any) {
        document.getElementById("rootMainContainer")?.classList.add(styles.forceOverflowYHidden);
    }
}


// --------- //
// - ROOMS - //
// --------- //

export class JC_Utils_Rooms {

    // Parse rooms JSON string into array
    static parseRoomsJson(roomsListJson?: string): string[] {
        if (!roomsListJson) return [];
        try {
            return JSON.parse(roomsListJson) || [];
        } catch (error) {
            console.error('Error parsing rooms JSON:', error);
            return [];
        }
    }

    // Get selected room names from rooms array
    static getSelectedRoomNames(rooms: string[], _selectedRooms: string[]): string[] {
        // For now, just return all rooms since we don't have selection logic
        return rooms;
    }
}


// ----------- //
// - DEFECTS - //
// ----------- //

export class JC_Utils_Defects {

    // Count DefectImages for a specific defect
    static countDefectImages(defectId: string, defectImages: any[]): number {
        return defectImages.filter(image => image.DefectId === defectId).length;
    }

    // Get defects for a specific room
    static getDefectsForRoom(_roomName: string, allDefects: any[], _roomOptions: any[]): any[] {
        // For now, return all defects since we don't have room-specific filtering
        // In a real implementation, you might filter by room or area
        return allDefects;
    }
}


// ----------- //
// - PRICING - //
// ----------- //

export class JC_Utils_Pricing {
    /**
     * Calculate Sales Price from Cost Price and Percent Markup
     * @param costPrice The cost price
     * @param percentMarkup The percent markup
     * @returns The calculated sales price
     */
    static calculateSalesPrice(costPrice: number, percentMarkup: number): number {
        const result = costPrice * (1 + percentMarkup / 100);
        return parseFloat(result.toFixed(2));
    }

    /**
     * Calculate Percent Markup from Cost Price and Sales Price
     * @param costPrice The cost price
     * @param salesPrice The sales price
     * @returns The calculated percent markup
     */
    static calculatePercentMarkup(costPrice: number, salesPrice: number): number {
        if (costPrice === 0) return 0;
        const result = ((salesPrice / costPrice) - 1) * 100;
        return parseFloat(result.toFixed(2));
    }

    /**
     * Calculate Sales Price from Cost Price and Profit Margin
     * @param costPrice The cost price
     * @param profitMargin The profit margin percentage
     * @returns The calculated sales price
     */
    static calculateSalesPriceFromMargin(costPrice: number, profitMargin: number): number {
        // For profit margin: Sales Price = Cost Price / (1 - (Profit Margin / 100))
        if (profitMargin >= 100) return costPrice * 100; // Prevent division by zero or negative
        const result = costPrice / (1 - (profitMargin / 100));
        return parseFloat(result.toFixed(2));
    }

    /**
     * Calculate Profit Margin from Cost Price and Sales Price
     * @param costPrice The cost price
     * @param salesPrice The sales price
     * @returns The calculated profit margin percentage
     */
    static calculateProfitMargin(costPrice: number, salesPrice: number): number {
        if (costPrice === 0 || salesPrice === 0) return 0;
        // For profit margin: ((Sales Price - Cost Price) / Sales Price) * 100
        const result = ((salesPrice - costPrice) / salesPrice) * 100;
        return parseFloat(result.toFixed(2));
    }
}