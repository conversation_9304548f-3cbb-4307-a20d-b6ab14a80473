import { NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { ContactModel } from "@/app/models/Contact";
import { JC_Utils_Business } from "@/app/Utils";

export const dynamic = 'force-dynamic';

// Get all Contact
export async function GET() {
    try {
        unstable_noStore();
        const pagingResult = await JC_Utils_Business.sqlGetList(ContactModel, undefined, {
            PageSize: undefined,
            PageIndex: undefined,
            SortField: "Id",
            SortAsc: true
        });
        return NextResponse.json({ result: pagingResult.ResultList }, { status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
