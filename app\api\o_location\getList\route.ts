import { NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { O_LocationModel } from "@/app/models/O_Location";
import { JC_Utils_Business } from "@/app/Utils";

export const dynamic = 'force-dynamic';

// Get all O_Location
export async function GET() {
    try {
        unstable_noStore();
        const pagingResult = await JC_Utils_Business.sqlGetList(O_LocationModel, undefined, {
            PageSize: undefined,
            PageIndex: undefined,
            SortField: "SortOrder",
            SortAsc: true
        });
        return NextResponse.json({ result: pagingResult.ResultList }, { status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
